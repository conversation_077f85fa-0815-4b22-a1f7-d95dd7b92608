package net.diaowen.dwsurvey.dao;

import net.diaowen.common.dao.BaseDao;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.TaskGrid;

import java.util.List;

/**
 * 任务网格区域DAO接口
 *
 * <AUTHOR> Team
 */
public interface TaskGridDao extends BaseDao<TaskGrid, String> {

    Page<TaskGrid> findByCondition(Page<TaskGrid> page, String gridCode, String gridName,
                                   String gridLevel, Integer status);

    TaskGrid findByGridCode(String gridCode);

    List<TaskGrid> findByParentGridCode(String parentGridCode);

    List<TaskGrid> findByGridLevel(String gridLevel);

    List<TaskGrid> findActiveGrids();

    TaskGrid findGridByPoint(Double longitude, Double latitude);

    List<TaskGrid> findChildGrids(String parentGridId);

    List<TaskGrid> findGridPath(String gridId);

    List<TaskGrid> findNearbyGrids(String gridId, Double radius);

    List<TaskGrid> findByArea(String area);

    List<TaskGrid> findGridsByBounds(Double minLng, Double maxLng, Double minLat, Double maxLat);

    void updateStatus(String gridId, Integer status);

    void batchUpdateStatus(List<String> gridIds, Integer status);

    boolean hasChildGrids(String gridId);

    Long countByLevel(int level);

    List<TaskGrid> findByLevel(Integer level);

    List<TaskGrid> findRootGrids();
}