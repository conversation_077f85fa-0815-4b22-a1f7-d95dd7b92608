package net.diaowen.dwsurvey.dao.impl;

import net.diaowen.common.dao.BaseDaoImpl;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.dao.TaskGridDao;
import net.diaowen.dwsurvey.entity.TaskGrid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.List;

/**
 * 任务网格区域DAO实现类
 * 
 * <AUTHOR> Team
 */
@Repository
public class TaskGridDaoImpl extends BaseDaoImpl<TaskGrid, String> implements TaskGridDao {

    @Autowired
    public TaskGridDaoImpl(EntityManager em) {
        super(TaskGrid.class, em);
    }

    @Override
    public Page<TaskGrid> findByCondition(Page<TaskGrid> page, String gridCode, String gridName,
                                          String gridLevel, Integer status) {
        String hql = "from TaskGrid t where 1=1";
        List<Object> params = new ArrayList<>();
        
        if (gridCode != null && !gridCode.isEmpty()) {
            hql += " and t.gridCode like ?";
            params.add("%" + gridCode + "%");
        }
        if (gridName != null && !gridName.isEmpty()) {
            hql += " and t.gridName like ?";
            params.add("%" + gridName + "%");
        }
        if (gridLevel != null && !gridLevel.isEmpty()) {
            hql += " and t.gridLevel = ?";
            params.add(gridLevel);
        }
        if (status != null) {
            hql += " and t.status = ?";
            params.add(status);
        }
        
        hql += " order by t.createTime desc";
        
        return findPage(page, hql, params.toArray());
    }

    @Override
    public TaskGrid findByGridCode(String gridCode) {
        String hql = "from TaskGrid t where t.gridCode = ?";
        return findUnique(hql, gridCode);
    }

    @Override
    public List<TaskGrid> findByParentGridCode(String parentGridCode) {
        String hql = "from TaskGrid t where t.parentGridCode = ? order by t.gridCode";
        return find(hql, parentGridCode);
    }

    @Override
    public List<TaskGrid> findByGridLevel(String gridLevel) {
        String hql = "from TaskGrid t where t.gridLevel = ? and t.status = 1 order by t.gridCode";
        return find(hql, gridLevel);
    }

    @Override
    public List<TaskGrid> findActiveGrids() {
        String hql = "from TaskGrid t where t.status = 1 order by t.gridLevel, t.gridCode";
        return find(hql);
    }

    @Override
    public TaskGrid findGridByPoint(Double longitude, Double latitude) {
        // 简化实现，实际应使用地理位置计算
        String hql = "from TaskGrid t where t.status = 1 order by t.gridLevel desc";
        List<TaskGrid> grids = find(hql);
        return grids.isEmpty() ? null : grids.get(0);
    }

    public List<TaskGrid> findChildGrids(String parentGridId) {
        String hql = "from TaskGrid t where t.parentGridId = ? and t.status = 1 order by t.gridCode";
        return find(hql, parentGridId);
    }

    public List<TaskGrid> findGridPath(String gridId) {
        List<TaskGrid> path = new ArrayList<>();
        TaskGrid grid = get(gridId);
        
        while (grid != null) {
            path.add(0, grid);
            if (grid.getParentId() != null) {
                grid = get(grid.getParentId());
            } else {
                break;
            }
        }
        
        return path;
    }

    public List<TaskGrid> findNearbyGrids(String gridId, Double radius) {
        // 简化实现，实际应使用地理位置计算
        TaskGrid currentGrid = get(gridId);
        if (currentGrid != null && currentGrid.getParentGridCode() != null) {
            return findByParentGridCode(currentGrid.getParentGridCode());
        }
        return new ArrayList<>();
    }

    // 添加缺少的方法实现
    public List<TaskGrid> findByArea(String area) {
        String hql = "from TaskGrid t where t.area = ? and t.status = 1 order by t.gridCode";
        return find(hql, area);
    }

    public List<TaskGrid> findGridsByBounds(Double minLng, Double maxLng, Double minLat, Double maxLat) {
        String hql = "from TaskGrid t where t.longitude between ? and ? and t.latitude between ? and ? and t.status = 1";
        return find(hql, minLng, maxLng, minLat, maxLat);
    }

    public void updateStatus(String gridId, Integer status) {
        String hql = "update TaskGrid set status = ? where id = ?";
        executeUpdate(hql, status, gridId);
    }

    public void batchUpdateStatus(List<String> gridIds, Integer status) {
        if (gridIds != null && !gridIds.isEmpty()) {
            String hql = "update TaskGrid set status = ? where id in (:ids)";
            getSession().createQuery(hql)
                .setParameter("status", status)
                .setParameterList("ids", gridIds)
                .executeUpdate();
        }
    }

    public boolean hasChildGrids(String gridId) {
        String hql = "select count(*) from TaskGrid where parentGridId = ? and status = 1";
        Object result = findUnique(hql, gridId);
        Long count = result instanceof Long ? (Long) result : 0L;
        return count != null && count > 0;
    }

    public Long countByLevel(int level) {
        String hql = "select count(*) from TaskGrid where gridLevel = ? and status = 1";
        Object result = findUnique(hql, String.valueOf(level));
        return result instanceof Long ? (Long) result : 0L;
    }

    @Override
    public List<TaskGrid> findByLevel(Integer level) {
        String hql = "from TaskGrid t where t.gridLevel = ? and t.status = 1 order by t.gridCode";
        return find(hql, level.toString());
    }

    @Override
    public List<TaskGrid> findRootGrids() {
        String hql = "from TaskGrid t where (t.parentGridId is null or t.parentGridId = '') and t.status = 1 order by t.gridCode";
        return find(hql);
    }
}