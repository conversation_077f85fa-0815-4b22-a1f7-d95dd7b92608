package net.diaowen.dwsurvey.dao.impl;

import net.diaowen.common.dao.BaseDaoImpl;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.dao.UserGridRoleDao;
import net.diaowen.dwsurvey.entity.UserGridRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户网格角色DAO实现类
 * 
 * <AUTHOR> Team
 */
@Repository
public class UserGridRoleDaoImpl extends BaseDaoImpl<UserGridRole, String> implements UserGridRoleDao {

    @Autowired
    public UserGridRoleDaoImpl(EntityManager em) {
        super(UserGridRole.class, em);
    }

    @Override
    public Page<UserGridRole> findByCondition(Page<UserGridRole> page, String userId, String gridCode,
                                              String roleName, Integer status) {
        String hql = "from UserGridRole t where 1=1";
        List<Object> params = new ArrayList<>();
        
        if (userId != null && !userId.isEmpty()) {
            hql += " and t.userId = ?";
            params.add(userId);
        }
        if (gridCode != null && !gridCode.isEmpty()) {
            hql += " and t.gridCode = ?";
            params.add(gridCode);
        }
        if (roleName != null && !roleName.isEmpty()) {
            hql += " and t.roleName = ?";
            params.add(roleName);
        }
        if (status != null) {
            hql += " and t.status = ?";
            params.add(status);
        }
        
        hql += " order by t.createTime desc";
        
        return findPage(page, hql, params.toArray());
    }

    @Override
    public List<UserGridRole> findByUserId(String userId) {
        String hql = "from UserGridRole t where t.userId = ? and t.status = 1 order by t.createTime desc";
        return find(hql, userId);
    }

    @Override
    public List<UserGridRole> findByGridCode(String gridCode) {
        String hql = "from UserGridRole t where t.gridCode = ? and t.status = 1 order by t.createTime desc";
        return find(hql, gridCode);
    }

    @Override
    public UserGridRole findByUserIdAndGridCode(String userId, String gridCode) {
        String hql = "from UserGridRole t where t.userId = ? and t.gridCode = ? and t.status = 1";
        return findUnique(hql, userId, gridCode);
    }

    @Override
    public List<UserGridRole> findByRoleType(String roleType) {
        String hql = "from UserGridRole t where t.roleName = ? and t.status = 1";
        return find(hql, roleType);
    }

    @Override
    public UserGridRole findByUserIdAndGridCodeAndRole(String userId, String gridCode, String roleName) {
        String hql = "from UserGridRole t where t.userId = ? and t.gridCode = ? and t.roleName = ? and t.status = 1";
        return findUnique(hql, userId, gridCode, roleName);
    }

    @Override
    public List<UserGridRole> findUsersByGridAndRole(String gridCode, String roleName) {
        String hql = "from UserGridRole t where t.gridCode = ? and t.roleName = ? and t.status = 1 order by t.createTime desc";
        return find(hql, gridCode, roleName);
    }

    @Override
    public List<UserGridRole> findGridsByUserAndRole(String userId, String roleName) {
        String hql = "from UserGridRole t where t.userId = ? and t.roleName = ? and t.status = 1 order by t.createTime desc";
        return find(hql, userId, roleName);
    }

    @Override
    public boolean hasPermission(String userId, String gridCode, String permission) {
        String hql = "select count(*) from UserGridRole t where t.userId = ? and t.gridCode = ? and t.permissions like ? and t.status = 1";
        Long count = (Long) findUnique(hql, userId, gridCode, "%" + permission + "%");
        return count != null && count > 0;
    }

    @Override
    public String getUserPermissions(String userId, String gridCode) {
        String hql = "select t.permissions from UserGridRole t where t.userId = ? and t.gridCode = ? and t.status = 1";
        List<String> permissionStrings = find(hql, userId, gridCode);
        
        if (permissionStrings != null && !permissionStrings.isEmpty()) {
            return permissionStrings.get(0);
        }
        
        return "";
    }

    @Override
    public List<UserGridRole> findActiveRolesByUser(String userId) {
        String hql = "from UserGridRole t where t.userId = ? and t.status = 1 and (t.endTime is null or t.endTime > current_timestamp()) order by t.createTime desc";
        return find(hql, userId);
    }

    @Override
    public List<UserGridRole> findExpiredRoles() {
        String hql = "from UserGridRole t where t.status = 1 and t.endTime is not null and t.endTime <= current_timestamp()";
        return find(hql);
    }

    @Override
    public Page<UserGridRole> findRolesByGrid(Page<UserGridRole> page, String gridCode, String roleName, Integer status) {
        String hql = "from UserGridRole t where t.gridCode = ?";
        List<Object> params = new ArrayList<>();
        params.add(gridCode);
        
        if (roleName != null && !roleName.isEmpty()) {
            hql += " and t.roleName = ?";
            params.add(roleName);
        }
        if (status != null) {
            hql += " and t.status = ?";
            params.add(status);
        }
        
        hql += " order by t.createTime desc";
        
        return findPage(page, hql, params.toArray());
    }

    @Override
    public List<UserGridRole> findRolesByUserAndStatus(String userId, Integer status) {
        String hql = "from UserGridRole t where t.userId = ? and t.status = ? order by t.createTime desc";
        return find(hql, userId, status);
    }

    @Override
    public Page<UserGridRole> findRolesByUser(Page<UserGridRole> page, String userId, String gridCode, Integer status) {
        String hql = "from UserGridRole t where t.userId = ?";
        List<Object> params = new ArrayList<>();
        params.add(userId);
        
        if (gridCode != null && !gridCode.isEmpty()) {
            hql += " and t.gridCode = ?";
            params.add(gridCode);
        }
        if (status != null) {
            hql += " and t.status = ?";
            params.add(status);
        }
        
        hql += " order by t.createTime desc";
        
        return findPage(page, hql, params.toArray());
    }
}