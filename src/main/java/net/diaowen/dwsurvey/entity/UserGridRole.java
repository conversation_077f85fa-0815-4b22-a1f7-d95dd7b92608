package net.diaowen.dwsurvey.entity;

import net.diaowen.common.base.entity.IdEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 用户网格角色权限实体类
 * 
 * <AUTHOR> Team
 */
@Entity
@Table(name = "t_user_grid_role")
@DynamicInsert
@DynamicUpdate
public class UserGridRole extends IdEntity {

    private String userId;          // 用户ID
    private String userName;        // 用户名称
    private String gridCode;        // 网格编码
    private String gridName;        // 网格名称
    private String roleType;        // 角色类型 (GRID_ADMIN-网格管理员, DATA_COLLECTOR-数据采集员, SUPERVISOR-监督员)
    private String permissions;     // 权限JSON (CREATE, READ, UPDATE, DELETE, ASSIGN)
    private Integer status;         // 状态 (0-禁用, 1-启用)
    private Date createTime;        // 创建时间
    private Date updateTime;        // 更新时间
    private String createdBy;       // 创建人
    private String updatedBy;       // 更新人
    private String remark;          // 备注

    @Column(name = "user_id", length = 55)
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Column(name = "user_name", length = 100)
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Column(name = "grid_code", length = 50)
    public String getGridCode() {
        return gridCode;
    }

    public void setGridCode(String gridCode) {
        this.gridCode = gridCode;
    }

    @Column(name = "grid_name", length = 100)
    public String getGridName() {
        return gridName;
    }

    public void setGridName(String gridName) {
        this.gridName = gridName;
    }

    @Column(name = "role_type", length = 50)
    public String getRoleType() {
        return roleType;
    }

    public void setRoleType(String roleType) {
        this.roleType = roleType;
    }

    @Column(name = "permissions", length = 500)
    public String getPermissions() {
        return permissions;
    }

    public void setPermissions(String permissions) {
        this.permissions = permissions;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Column(name = "created_by", length = 55)
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "updated_by", length = 55)
    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "remark", length = 500)
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 兼容性方法，用于支持旧代码
    public String getRoleName() {
        return roleType;
    }

    public void setRoleName(String roleName) {
        this.roleType = roleName;
    }

    public String getAssignedBy() {
        return createdBy;
    }

    public void setAssignedBy(String assignedBy) {
        this.createdBy = assignedBy;
    }

    public String getRevokedBy() {
        return updatedBy;
    }

    public void setRevokedBy(String revokedBy) {
        this.updatedBy = revokedBy;
    }

    public void setAssignedDate(Date date) {
        this.createTime = date;
    }

    public void setRevokedDate(Date date) {
        this.updateTime = date;
    }

    @PrePersist
    public void prePersist() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        updateTime = now;
        if (status == null) {
            status = 1; // 默认启用状态
        }
    }

    @PreUpdate
    public void preUpdate() {
        updateTime = new Date();
    }
}