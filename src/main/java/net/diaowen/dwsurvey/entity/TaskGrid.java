package net.diaowen.dwsurvey.entity;

import net.diaowen.common.base.entity.IdEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 任务网格区域实体类
 * 
 * <AUTHOR> Team
 */
@Entity
@Table(name = "t_task_grid")
@DynamicInsert
@DynamicUpdate
public class TaskGrid extends IdEntity {

    private String gridCode;        // 网格编码
    private String gridName;        // 网格名称
    private String gridLevel;       // 网格级别 (1-省级, 2-市级, 3-区县级, 4-街道级, 5-社区级)
    private String parentGridCode;  // 父网格编码
    private String gridBoundary;    // 网格边界坐标JSON
    private String centerPoint;     // 网格中心点坐标
    private String description;     // 网格描述
    private Integer status;         // 状态 (0-禁用, 1-启用)
    private Date createTime;        // 创建时间
    private Date updateTime;        // 更新时间
    private String createdBy;       // 创建人
    private String updatedBy;       // 更新人

    @Column(name = "grid_code", length = 50, unique = true)
    public String getGridCode() {
        return gridCode;
    }

    public void setGridCode(String gridCode) {
        this.gridCode = gridCode;
    }

    @Column(name = "grid_name", length = 100)
    public String getGridName() {
        return gridName;
    }

    public void setGridName(String gridName) {
        this.gridName = gridName;
    }

    @Column(name = "grid_level", length = 10)
    public String getGridLevel() {
        return gridLevel;
    }

    public void setGridLevel(String gridLevel) {
        this.gridLevel = gridLevel;
    }

    @Column(name = "parent_grid_code", length = 50)
    public String getParentGridCode() {
        return parentGridCode;
    }

    public void setParentGridCode(String parentGridCode) {
        this.parentGridCode = parentGridCode;
    }

    @Column(name = "grid_boundary", columnDefinition = "TEXT")
    public String getGridBoundary() {
        return gridBoundary;
    }

    public void setGridBoundary(String gridBoundary) {
        this.gridBoundary = gridBoundary;
    }

    @Column(name = "center_point", length = 100)
    public String getCenterPoint() {
        return centerPoint;
    }

    public void setCenterPoint(String centerPoint) {
        this.centerPoint = centerPoint;
    }

    @Column(name = "description", length = 500)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Column(name = "created_by", length = 55)
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "updated_by", length = 55)
    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @PrePersist
    public void prePersist() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        updateTime = now;
        if (status == null) {
            status = 1; // 默认启用状态
        }
    }

    @PreUpdate
    public void preUpdate() {
        updateTime = new Date();
    }

    // 兼容性方法 - 为了支持现有代码中的getParentId()调用
    @Transient
    public String getParentId() {
        return parentGridCode;
    }

    // 兼容性方法 - 为了支持现有代码中的getParentGridId()调用
    @Transient
    public String getParentGridId() {
        return parentGridCode;
    }

    // 从中心点坐标解析经度
    @Transient
    public Double getLongitude() {
        if (centerPoint != null && centerPoint.contains(",")) {
            try {
                String[] coords = centerPoint.split(",");
                return Double.parseDouble(coords[0].trim());
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    // 从中心点坐标解析纬度
    @Transient
    public Double getLatitude() {
        if (centerPoint != null && centerPoint.contains(",")) {
            try {
                String[] coords = centerPoint.split(",");
                return Double.parseDouble(coords[1].trim());
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }
}