package net.diaowen.dwsurvey.service;

import net.diaowen.common.plugs.page.Page;
import net.diaowen.common.service.BaseService;
import net.diaowen.dwsurvey.entity.TaskGrid;

import java.util.List;

/**
 * 任务网格区域管理服务接口
 *
 * <AUTHOR> Team
 */
public interface TaskGridManager extends BaseService<TaskGrid, String> {

    Page<TaskGrid> findByCondition(Page<TaskGrid> page, String keyword, String parentId);

    TaskGrid findByGridCode(String gridCode);

    List<TaskGrid> findByParentGridCode(String parentGridCode);

    List<TaskGrid> findByGridLevel(String gridLevel);

    List<TaskGrid> findActiveGrids();

    TaskGrid findGridByPoint(Double longitude, Double latitude);

    TaskGrid createGrid(TaskGrid grid, String createdBy);

    TaskGrid updateGrid(TaskGrid grid, String updatedBy);

    boolean deleteGrid(String gridId, String userId);

    boolean updateGridStatus(String gridId, Integer status, String updatedBy);

    List<TaskGrid> findChildGrids(String parentGridId);

    List<TaskGrid> findGridPath(String gridId);

    List<TaskGrid> findNearbyGrids(String gridId, Double radius);

    List<TaskGrid> findByLevel(Integer level);

    List<TaskGrid> findRootGrids();

    List<TaskGrid> findByArea(String area);



    List<TaskGrid> findGridsByBounds(Double minLng, Double minLat, Double maxLng, Double maxLat);

    void updateStatus(String gridId, Integer status);

    void batchUpdateStatus(List<String> gridIds, Integer status);

    boolean hasChildGrids(String gridId);

    long countByLevel(int level);

    List<TaskGrid> findGridHierarchy(String rootGridId);

    boolean validateGridBounds(Double minLng, Double maxLng, Double minLat, Double maxLat);

    List<TaskGrid> buildGridTree(String rootId);

    List<TaskGrid> getGridPath(String gridId);

    Page<TaskGrid> searchGrids(Page<TaskGrid> page, String keyword);

    List<TaskGrid> findNearbyGrids(Double longitude, Double latitude, Double radius);

    List<TaskGrid> findByParentId(String parentId);
}