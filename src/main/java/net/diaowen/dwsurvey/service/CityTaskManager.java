package net.diaowen.dwsurvey.service;

import net.diaowen.common.base.service.BaseService;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.dwsurvey.entity.CityTask;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CityTaskManager extends BaseService<CityTask, String> {

    boolean assignTask(String taskId, String gridId, String userId, String assignedBy);

    Page<CityTask> findMyTasks(Page<CityTask> page, String gridCode, Integer status);

    List<CityTask> findTasksByGrid(String gridCode, Integer status);

    Map<String, Object> getTaskStatsByUser(String userId);

    List<CityTask> findNearbyTasks(Double longitude, Double latitude, Double radius, String userId);

    List<CityTask> findTasksNearDeadline(String userId, Integer hours);

    Page<CityTask> searchTasks(Page<CityTask> page, String userId, String keyword);

    int batchUpdateStatus(List<String> taskIds, Integer status, String updatedBy);

    List<CityTask> autoAssignTasks(String userId, String gridCode);

    List<CityTask> smartSortTasks(List<CityTask> tasks, String sortType, Double userLongitude, Double userLatitude);

    boolean completeTask(String taskId, String formData, String attachments, String userId);

    CityTask getTaskDetail(String taskId, String userId);

    CityTask createTask(CityTask task, String createdBy);

    CityTask updateTask(CityTask task, String updatedBy);

    boolean deleteTask(String taskId, String userId);

    List<CityTask> findUnassignedTasks();

    List<CityTask> findOverdueTasks(String userId);

    boolean updateTaskStatus(String taskId, Integer status, String updatedBy);

    Map<String, Object> generateDailyStatistics(Date date);

    int countActiveTasksByAssignee(String userId);



    List<CityTask> findUrgentTasks(String userId);

    boolean hasTaskPermission(String userId, String taskId, String permission);
}