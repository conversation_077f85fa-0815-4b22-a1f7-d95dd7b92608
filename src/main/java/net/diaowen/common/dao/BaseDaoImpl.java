package net.diaowen.common.dao;

import net.diaowen.common.base.dao.BaseDao;
import net.diaowen.common.plugs.page.Page;
import net.diaowen.common.plugs.page.PageRequest;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.query.Query;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;

import javax.persistence.EntityManager;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public abstract class BaseDaoImpl<T, ID extends Serializable> extends SimpleJpaRepository<T, ID> implements BaseDao<T, ID>, ISimpleHibernateDao<T, ID> {

    private final EntityManager entityManager;

    public BaseDaoImpl(Class<T> domainClass, EntityManager em) {
        super(domainClass, em);
        this.entityManager = em;
    }

    public Session getSession() {
        return entityManager.unwrap(Session.class);
    }

    // 实现BaseDao接口的find方法
    @Override
    @SuppressWarnings("unchecked")
    public List<T> find(String hql, Object... params) {
        Query<T> query = getSession().createQuery(hql, getDomainClass());
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                query.setParameter(i, params[i]);
            }
        }
        return query.list();
    }

    // 实现BaseDao接口的findUnique方法
    @Override
    @SuppressWarnings("unchecked")
    public T findUnique(String hql, Object... params) {
        Query<T> query = getSession().createQuery(hql, getDomainClass());
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                query.setParameter(i, params[i]);
            }
        }
        return query.uniqueResult();
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public Page<T> findPage(Page<T> page, String hql, Object... params) {
        // Count query
        String countHql = "select count(*) " + hql;
        Query<Long> countQuery = getSession().createQuery(countHql, Long.class);
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                countQuery.setParameter(i, params[i]);
            }
        }
        Long total = countQuery.uniqueResult();
        page.setTotal(total.intValue());

        // Data query
        Query<T> dataQuery = getSession().createQuery(hql, (Class<T>) getDomainClass());
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                dataQuery.setParameter(i, params[i]);
            }
        }
        dataQuery.setFirstResult((page.getPageNo() - 1) * page.getPageSize());
        dataQuery.setMaxResults(page.getPageSize());
        page.setList(dataQuery.list());

        return page;
    }



    protected int executeUpdate(String hql, Object... params) {
        Query<?> query = getSession().createQuery(hql);
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                query.setParameter(i, params[i]);
            }
        }
        return query.executeUpdate();
    }

    @Override
    public Page<T> findByCondition(Page<T> page, List<Criterion> criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }

        // Get total count
        Number totalCount = (Number) criteria.setProjection(Projections.rowCount()).uniqueResult();
        if (totalCount != null) {
            page.setTotal(totalCount.intValue());
        } else {
            page.setTotal(0);
        }

        // Reset projection and get paginated results
        criteria.setProjection(null);
        criteria.setResultTransformer(Criteria.ROOT_ENTITY);
        criteria.setFirstResult((page.getPageNo() - 1) * page.getPageSize());
        criteria.setMaxResults(page.getPageSize());
        page.setList(criteria.list());
        return page;
    }

    // 默认实现findPageOderBy方法
    public Page<T> findPageOderBy(Page<T> page, String orderBy, boolean isAsc, List<Criterion> criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }

        // Add ordering
        if (orderBy != null && !orderBy.trim().isEmpty()) {
            if (isAsc) {
                criteria.addOrder(Order.asc(orderBy));
            } else {
                criteria.addOrder(Order.desc(orderBy));
            }
        }

        // Get total count
        Number totalCount = (Number) criteria.setProjection(Projections.rowCount()).uniqueResult();
        if (totalCount != null) {
            page.setTotal(totalCount.intValue());
        } else {
            page.setTotal(0);
        }

        // Reset projection and get paginated results
        criteria.setProjection(null);
        criteria.setResultTransformer(Criteria.ROOT_ENTITY);
        criteria.setFirstResult((page.getPageNo() - 1) * page.getPageSize());
        criteria.setMaxResults(page.getPageSize());
        page.setList(criteria.list());
        return page;
    }

    // 兼容性方法 - 支持Criterion...参数
    public Page<T> findPageOderBy(Page<T> page, String orderBy, boolean isAsc, Criterion... criterions) {
        List<Criterion> criterionList = new ArrayList<>();
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criterionList.add(criterion);
            }
        }
        return findPageOderBy(page, orderBy, isAsc, criterionList);
    }

    // 添加findAll(CriteriaQuery)方法
    @SuppressWarnings("unchecked")
    public List<T> findAll(javax.persistence.criteria.CriteriaQuery criteriaQuery) {
        return entityManager.createQuery(criteriaQuery).getResultList();
    }

    // 添加findPageByCri方法
    public Page<T> findPageByCri(Page<T> page, List<Criterion> criterions) {
        return findByCondition(page, criterions);
    }

    // 添加findList方法
    @SuppressWarnings("unchecked")
    public List<Object[]> findList(String hql, Object... params) {
        org.hibernate.query.Query query = getSession().createQuery(hql);
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                query.setParameter(i, params[i]);
            }
        }
        return query.list();
    }

    // 添加findUniObjs方法
    @SuppressWarnings("unchecked")
    public Object[] findUniObjs(String hql, Object... params) {
        org.hibernate.query.Query query = getSession().createQuery(hql);
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                query.setParameter(i, params[i]);
            }
        }
        return (Object[]) query.uniqueResult();
    }

    // 添加findFirst方法
    @SuppressWarnings("unchecked")
    public T findFirst(String orderBy, boolean isAsc, Criterion... criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }

        // Add ordering
        if (orderBy != null && !orderBy.trim().isEmpty()) {
            if (isAsc) {
                criteria.addOrder(Order.asc(orderBy));
            } else {
                criteria.addOrder(Order.desc(orderBy));
            }
        }

        criteria.setMaxResults(1);
        return (T) criteria.uniqueResult();
    }

    // 添加findFirst方法 - List<Criterion>版本
    @SuppressWarnings("unchecked")
    public T findFirst(String orderBy, boolean isAsc, List<Criterion> criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }

        // Add ordering
        if (orderBy != null && !orderBy.trim().isEmpty()) {
            if (isAsc) {
                criteria.addOrder(Order.asc(orderBy));
            } else {
                criteria.addOrder(Order.desc(orderBy));
            }
        }

        criteria.setMaxResults(1);
        return (T) criteria.uniqueResult();
    }

    // 添加findFirst方法 - 只有List<Criterion>参数
    @SuppressWarnings("unchecked")
    public T findFirst(List<Criterion> criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }

        criteria.setMaxResults(1);
        return (T) criteria.uniqueResult();
    }

    // 添加findFirst方法 - 只有Criterion...参数
    @SuppressWarnings("unchecked")
    public T findFirst(Criterion... criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }

        criteria.setMaxResults(1);
        return (T) criteria.uniqueResult();
    }

    // 添加findByOrder方法
    @SuppressWarnings("unchecked")
    public List<T> findByOrder(String orderByProperty, boolean isAsc, Criterion... criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }

        // Add ordering
        if (orderByProperty != null && !orderByProperty.trim().isEmpty()) {
            if (isAsc) {
                criteria.addOrder(Order.asc(orderByProperty));
            } else {
                criteria.addOrder(Order.desc(orderByProperty));
            }
        }

        return criteria.list();
    }

    // 添加getModel方法
    public T getModel(ID id) {
        T t = null;
        try {
            if (id != null && !"".equals(id)) {
                t = findById(id).orElse(null);
            }
            if (t == null) {
                t = getDomainClass().newInstance();
            }
        } catch (InstantiationException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return t;
    }

    // 添加findPageCriteria方法
    @SuppressWarnings("unchecked")
    public Page<T> findPageCriteria(PageRequest pageRequest, Criteria criteria) {
        Page<T> page = new Page<T>();
        page.setPageNo(pageRequest.getPageNo());
        page.setPageSize(pageRequest.getPageSize());

        // 获取总记录数
        Criteria countCriteria = getSession().createCriteria(getDomainClass());
        // 复制查询条件
        // 这里简化处理，实际应该复制所有条件
        countCriteria.setProjection(Projections.rowCount());
        Long totalCount = (Long) countCriteria.uniqueResult();
        page.setTotal(totalCount.intValue());

        // 设置分页
        criteria.setFirstResult((pageRequest.getPageNo() - 1) * pageRequest.getPageSize());
        criteria.setMaxResults(pageRequest.getPageSize());

        List<T> result = criteria.list();
        page.setResult(result);

        return page;
    }

    // 添加findPageList方法
    @SuppressWarnings("unchecked")
    public Page<T> findPageList(PageRequest pageRequest, List<Criterion> criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }
        return findPageCriteria(pageRequest, criteria);
    }

    // 添加findPage方法 - Criterion...版本
    @SuppressWarnings("unchecked")
    public Page<T> findPage(PageRequest pageRequest, Criterion... criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }
        return findPageCriteria(pageRequest, criteria);
    }

    // 添加findPage方法 - HQL版本
    @SuppressWarnings("unchecked")
    public Page<T> findPage(PageRequest pageRequest, String hql, Map<String, ?> params) {
        Page<T> page = new Page<T>();
        page.setPageNo(pageRequest.getPageNo());
        page.setPageSize(pageRequest.getPageSize());

        // 获取总记录数
        String countHql = "select count(*) " + hql;
        org.hibernate.query.Query countQuery = getSession().createQuery(countHql);
        if (params != null) {
            for (Map.Entry<String, ?> entry : params.entrySet()) {
                countQuery.setParameter(entry.getKey(), entry.getValue());
            }
        }
        Long totalCount = (Long) countQuery.uniqueResult();
        page.setTotal(totalCount.intValue());

        // 获取数据
        org.hibernate.query.Query query = getSession().createQuery(hql);
        if (params != null) {
            for (Map.Entry<String, ?> entry : params.entrySet()) {
                query.setParameter(entry.getKey(), entry.getValue());
            }
        }
        query.setFirstResult((pageRequest.getPageNo() - 1) * pageRequest.getPageSize());
        query.setMaxResults(pageRequest.getPageSize());

        List<T> result = query.list();
        page.setResult(result);

        return page;
    }

    // 添加findPage方法 - 只有HQL版本
    @SuppressWarnings("unchecked")
    public Page<T> findPage(PageRequest pageRequest, String hql) {
        return findPage(pageRequest, hql, (Map<String, ?>) null);
    }

    // 添加findPage方法 - HQL + Object...版本
    @SuppressWarnings("unchecked")
    public Page<T> findPage(PageRequest pageRequest, String hql, Object... params) {
        Page<T> page = new Page<T>();
        page.setPageNo(pageRequest.getPageNo());
        page.setPageSize(pageRequest.getPageSize());

        // 获取总记录数
        String countHql = "select count(*) " + hql;
        org.hibernate.query.Query countQuery = getSession().createQuery(countHql);
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                countQuery.setParameter(i, params[i]);
            }
        }
        Long totalCount = (Long) countQuery.uniqueResult();
        page.setTotal(totalCount.intValue());

        // 获取数据
        org.hibernate.query.Query query = getSession().createQuery(hql);
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                query.setParameter(i, params[i]);
            }
        }
        query.setFirstResult((pageRequest.getPageNo() - 1) * pageRequest.getPageSize());
        query.setMaxResults(pageRequest.getPageSize());

        List<T> result = query.list();
        page.setResult(result);

        return page;
    }

    // 添加getAll方法 - PageRequest版本
    @SuppressWarnings("unchecked")
    public Page<T> getAll(PageRequest pageRequest) {
        Page<T> page = new Page<T>();
        page.setPageNo(pageRequest.getPageNo());
        page.setPageSize(pageRequest.getPageSize());

        // 获取总记录数
        String countHql = "select count(*) from " + getDomainClass().getSimpleName();
        org.hibernate.query.Query countQuery = getSession().createQuery(countHql);
        Long totalCount = (Long) countQuery.uniqueResult();
        page.setTotal(totalCount.intValue());

        // 获取数据
        String hql = "from " + getDomainClass().getSimpleName();
        org.hibernate.query.Query query = getSession().createQuery(hql);
        query.setFirstResult((pageRequest.getPageNo() - 1) * pageRequest.getPageSize());
        query.setMaxResults(pageRequest.getPageSize());

        List<T> result = query.list();
        page.setResult(result);

        return page;
    }

    // 添加isPropertyUnique方法
    @SuppressWarnings("unchecked")
    public boolean isPropertyUnique(String propertyName, Object value, Object id) {
        String hql = "select count(*) from " + getDomainClass().getSimpleName() + " where " + propertyName + " = :value";
        if (id != null) {
            hql += " and id != :id";
        }

        org.hibernate.query.Query query = getSession().createQuery(hql);
        query.setParameter("value", value);
        if (id != null) {
            query.setParameter("id", id);
        }

        Long count = (Long) query.uniqueResult();
        return count == 0;
    }

    // 添加getIdName方法
    public String getIdName() {
        return "id";
    }

    // 添加distinct方法
    public Criteria distinct(Criteria criteria) {
        return criteria.setResultTransformer(Criteria.DISTINCT_ROOT_ENTITY);
    }

    // 添加distinct方法 - Query版本
    public org.hibernate.query.Query distinct(org.hibernate.query.Query query) {
        // Query本身不支持distinct，这里返回原query
        return query;
    }

    // 添加initProxyObject方法
    public void initProxyObject(Object proxy) {
        // 这里可以初始化代理对象，暂时什么都不做
    }

    // 添加createCriteria方法 - List<Criterion>版本
    public Criteria createCriteria(List<Criterion> criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }
        return criteria;
    }

    // 添加createCriteria方法 - Criterion...版本
    public Criteria createCriteria(Criterion... criterions) {
        Criteria criteria = getSession().createCriteria(getDomainClass());
        if (criterions != null) {
            for (Criterion criterion : criterions) {
                criteria.add(criterion);
            }
        }
        return criteria;
    }

    // 添加findUnique方法 - Criterion...版本
    public T findUnique(Criterion... criterions) {
        Criteria criteria = createCriteria(criterions);
        return (T) criteria.uniqueResult();
    }

    // 实现BaseDao接口的get方法
    @Override
    public T get(ID id) {
        return findById(id).orElse(null);
    }

    // 添加get(String)方法的重载
    @SuppressWarnings("unchecked")
    public T get(String id) {
        return findById((ID) id).orElse(null);
    }

    // 添加delete(String)方法的重载
    public void delete(String id) {
        deleteById((ID) id);
    }

    // 实现BaseDao接口的update方法
    @Override
    public void update(T entity) {
        super.save(entity);
    }

    // 实现ISimpleHibernateDao接口的泛型find方法
    @SuppressWarnings("unchecked")
    public <X> List<X> findGeneric(String hql, Object... params) {
        Query<X> query = getSession().createQuery(hql);
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                query.setParameter(i, params[i]);
            }
        }
        return query.list();
    }

    // 实现ISimpleHibernateDao接口的泛型findUnique方法
    @SuppressWarnings("unchecked")
    public <X> X findUniqueGeneric(String hql, Object... params) {
        Query<X> query = getSession().createQuery(hql);
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                query.setParameter(i, params[i]);
            }
        }
        return query.uniqueResult();
    }

    // 实现ISimpleHibernateDao接口的方法
    @Override
    public List<T> get(Collection<ID> ids) {
        return findAllById(ids);
    }

    @Override
    public List<T> getAll(String orderByProperty, boolean isAsc) {
        String direction = isAsc ? "ASC" : "DESC";
        String hql = "FROM " + getDomainClass().getSimpleName() + " ORDER BY " + orderByProperty + " " + direction;
        Query<T> query = getSession().createQuery(hql, getDomainClass());
        return query.list();
    }

    @Override
    public List<T> findBy(String propertyName, Object value) {
        String hql = "FROM " + getDomainClass().getSimpleName() + " WHERE " + propertyName + " = :value";
        Query<T> query = getSession().createQuery(hql, getDomainClass());
        query.setParameter("value", value);
        return query.list();
    }

    @Override
    public T findUniqueBy(String propertyName, Object value) {
        String hql = "FROM " + getDomainClass().getSimpleName() + " WHERE " + propertyName + " = :value";
        Query<T> query = getSession().createQuery(hql, getDomainClass());
        query.setParameter("value", value);
        return query.uniqueResult();
    }

    @Override
    public <X> List<X> find(String hql, Map<String, ?> values) {
        Query<X> query = getSession().createQuery(hql);
        if (values != null) {
            for (Map.Entry<String, ?> entry : values.entrySet()) {
                query.setParameter(entry.getKey(), entry.getValue());
            }
        }
        return query.list();
    }

    @Override
    public <X> X findUnique(String hql, Map<String, ?> values) {
        Query<X> query = getSession().createQuery(hql);
        if (values != null) {
            for (Map.Entry<String, ?> entry : values.entrySet()) {
                query.setParameter(entry.getKey(), entry.getValue());
            }
        }
        return query.uniqueResult();
    }

    @Override
    public int batchExecute(String hql, Object... values) {
        Query query = getSession().createQuery(hql);
        if (values != null) {
            for (int i = 0; i < values.length; i++) {
                query.setParameter(i, values[i]);
            }
        }
        return query.executeUpdate();
    }

    @Override
    public int batchExecute(String hql, Map<String, ?> values) {
        Query query = getSession().createQuery(hql);
        if (values != null) {
            for (Map.Entry<String, ?> entry : values.entrySet()) {
                query.setParameter(entry.getKey(), entry.getValue());
            }
        }
        return query.executeUpdate();
    }

    @Override
    public Query createQuery(String queryString, Object... values) {
        Query query = getSession().createQuery(queryString);
        if (values != null) {
            for (int i = 0; i < values.length; i++) {
                query.setParameter(i, values[i]);
            }
        }
        return query;
    }

    @Override
    public Query createQuery(String queryString, Map<String, ?> values) {
        Query query = getSession().createQuery(queryString);
        if (values != null) {
            for (Map.Entry<String, ?> entry : values.entrySet()) {
                query.setParameter(entry.getKey(), entry.getValue());
            }
        }
        return query;
    }

    // 实现ISimpleHibernateDao接口的其他缺失方法
    @Override
    public List<T> getAll() {
        return findAll();
    }

    @Override
    public SessionFactory getSessionFactory() {
        return getSession().getSessionFactory();
    }

    @Override
    public void setSessionFactory(SessionFactory sessionFactory) {
        // 在Spring Data JPA环境中，SessionFactory由EntityManager管理，不需要手动设置
    }

    @Override
    public List<T> find(Criterion... criterions) {
        Criteria criteria = createCriteria(criterions);
        return criteria.list();
    }


}
